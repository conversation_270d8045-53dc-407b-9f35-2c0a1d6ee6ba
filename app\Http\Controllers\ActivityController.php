<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\ActivityCategory;
use App\Models\ActivityImage;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;

class ActivityController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // ตรวจสอบว่าเป็น Admin route หรือไม่
        if ($request->is('admin/*')) {
            $activities = Activity::with(['category', 'images'])->latest()->get();
            return view('admin.activities.index', compact('activities'));
        }

        // หน้าบ้านปกติ
        $query = Activity::with(['category', 'images'])->published()->latest();

        // Filter by category if specified
        if ($request->has('category') && $request->category) {
            $query->where('category_id', $request->category);
        }

        $activities = $query->get();
        $categories = ActivityCategory::active()->get();

        return view('activities.index', compact('activities', 'categories'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Activity  $activity
     * @return \Illuminate\Http\Response
     */
    public function show(Activity $activity)
    {
        // Load relationships
        $activity->load(['category', 'images']);

        // Get related activities from the same category
        $relatedActivities = Activity::with(['category', 'images'])
            ->published()
            ->where('category_id', $activity->category_id)
            ->where('id', '!=', $activity->id)
            ->latest()
            ->limit(3)
            ->get();

        return view('activities.show', compact('activity', 'relatedActivities'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = ActivityCategory::active()->get();

        // ถ้าไม่มีหมวดหมู่ ให้สร้างหมวดหมู่เริ่มต้น
        if ($categories->isEmpty()) {
            ActivityCategory::create([
                'name' => 'งานบุญประจำปี',
                'description' => 'งานบุญประจำปีของวัด',
                'color' => '#007bff',
                'is_active' => true,
            ]);

            ActivityCategory::create([
                'name' => 'งานพิเศษ',
                'description' => 'งานพิเศษต่างๆ',
                'color' => '#28a745',
                'is_active' => true,
            ]);

            ActivityCategory::create([
                'name' => 'กิจกรรมชุมชน',
                'description' => 'กิจกรรมเพื่อชุมชน',
                'color' => '#ffc107',
                'is_active' => true,
            ]);

            $categories = ActivityCategory::active()->get();
        }

        return view('admin.activities.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validate all fields except is_published first
            $data = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'category_id' => 'required|exists:activity_categories,id',
                'activity_date' => 'nullable|date',
                'location' => 'nullable|string|max:255',
                'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
                'gallery_images.*' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
                'captions.*' => 'nullable|string|max:255',
            ]);

            // Handle checkbox separately - checkbox sends "on" when checked, nothing when unchecked
            $data['is_published'] = $request->has('is_published') ? true : false;

            // Handle cover image upload
            if ($request->hasFile('cover_image')) {
                try {
                    $imageErrors = ImageHelper::validateImage($request->file('cover_image'));
                    if (!empty($imageErrors)) {
                        return back()->withErrors(['cover_image' => implode(', ', $imageErrors)])->withInput();
                    }
                    $data['cover_image'] = ImageHelper::uploadAndResize($request->file('cover_image'), 'activities');
                } catch (\Exception $e) {
                    return back()->withErrors(['cover_image' => 'เกิดข้อผิดพลาดในการอัปโหลดรูปภาพหน้าปก'])->withInput();
                }
            }

            $activity = Activity::create($data);

            // Handle gallery images
            if ($request->hasFile('gallery_images')) {
                foreach ($request->file('gallery_images') as $index => $image) {
                    if ($image && $image->isValid()) {
                        try {
                            $imageErrors = ImageHelper::validateImage($image);
                            if (empty($imageErrors)) {
                                $imagePath = ImageHelper::uploadAndResize($image, 'activities/gallery');
                                ActivityImage::create([
                                    'activity_id' => $activity->id,
                                    'image_path' => $imagePath,
                                    'caption' => $request->captions[$index] ?? null,
                                    'sort_order' => $index,
                                ]);
                            }
                        } catch (\Exception $e) {
                            // Log error but continue with other images
                            \Log::error('Error uploading gallery image: ' . $e->getMessage());
                        }
                    }
                }
            }

            return redirect()->route('admin.activities.index')->with('success', 'เพิ่มกิจกรรมสำเร็จ');

        } catch (\Exception $e) {
            \Log::error('Error creating activity: ' . $e->getMessage());

            // If it's an AJAX request, return JSON response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาดในการบันทึกข้อมูล',
                    'error' => $e->getMessage()
                ], 500);
            }

            return back()->withErrors(['error' => 'เกิดข้อผิดพลาดในการบันทึกข้อมูล กรุณาลองใหม่อีกครั้ง'])->withInput();
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Activity $activity)
    {
        $activity->load(['category', 'images']);
        $categories = ActivityCategory::active()->get();
        return view('admin.activities.edit', compact('activity', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Activity $activity)
    {
        try {
            // Validate all fields except is_published first
            $data = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'category_id' => 'required|exists:activity_categories,id',
                'activity_date' => 'nullable|date',
                'location' => 'nullable|string|max:255',
                'cover_image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
                'gallery_images.*' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
                'captions.*' => 'nullable|string|max:255',
            ]);

            // Handle checkbox separately - checkbox sends "on" when checked, nothing when unchecked
            $data['is_published'] = $request->has('is_published') ? true : false;

            // Handle cover image upload
            if ($request->hasFile('cover_image')) {
                try {
                    $imageErrors = ImageHelper::validateImage($request->file('cover_image'));
                    if (!empty($imageErrors)) {
                        return back()->withErrors(['cover_image' => implode(', ', $imageErrors)])->withInput();
                    }

                    // Delete old cover image
                    if ($activity->cover_image) {
                        ImageHelper::deleteImage($activity->cover_image);
                    }

                    // Upload new cover image
                    $data['cover_image'] = ImageHelper::uploadAndResize($request->file('cover_image'), 'activities');
                } catch (\Exception $e) {
                    \Log::error('Error uploading cover image: ' . $e->getMessage());
                    return back()->withErrors(['cover_image' => 'เกิดข้อผิดพลาดในการอัปโหลดรูปภาพหน้าปก'])->withInput();
                }
            }

            $activity->update($data);

            // Handle gallery images
            if ($request->hasFile('gallery_images')) {
                foreach ($request->file('gallery_images') as $index => $image) {
                    if ($image && $image->isValid()) {
                        try {
                            $imageErrors = ImageHelper::validateImage($image);
                            if (empty($imageErrors)) {
                                $imagePath = ImageHelper::uploadAndResize($image, 'activities/gallery');
                                ActivityImage::create([
                                    'activity_id' => $activity->id,
                                    'image_path' => $imagePath,
                                    'caption' => $request->captions[$index] ?? null,
                                    'sort_order' => $activity->images()->count(),
                                ]);
                            }
                        } catch (\Exception $e) {
                            // Log error but continue with other images
                            \Log::error('Error uploading gallery image: ' . $e->getMessage());
                        }
                    }
                }
            }

            return redirect()->route('admin.activities.index')->with('success', 'อัปเดตกิจกรรมสำเร็จ');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            \Log::error('Error updating activity: ' . $e->getMessage());

            // If it's an AJAX request, return JSON response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาดในการอัปเดตข้อมูล',
                    'error' => $e->getMessage()
                ], 500);
            }

            return back()->withErrors(['error' => 'เกิดข้อผิดพลาดในการอัปเดตข้อมูล: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Activity $activity)
    {
        // Delete cover image
        ImageHelper::deleteImage($activity->cover_image);

        // Delete gallery images
        foreach ($activity->images as $image) {
            ImageHelper::deleteImage($image->image_path);
        }

        $activity->delete();

        return redirect()->route('admin.activities.index')->with('success', 'ลบกิจกรรมสำเร็จ');
    }

    /**
     * Delete a specific gallery image
     */
    public function deleteImage(Activity $activity, ActivityImage $image)
    {
        try {
            // Verify that the image belongs to this activity
            if ($image->activity_id !== $activity->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'ไม่พบรูปภาพที่ต้องการลบ'
                ], 404);
            }

            ImageHelper::deleteImage($image->image_path);
            $image->delete();

            return response()->json([
                'success' => true,
                'message' => 'ลบรูปภาพสำเร็จ'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error deleting image: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบรูปภาพ'
            ], 500);
        }
    }

    /**
     * Update image caption
     */
    public function updateImageCaption(Request $request, ActivityImage $image)
    {
        try {
            $request->validate([
                'caption' => 'nullable|string|max:255',
            ]);

            $image->update([
                'caption' => $request->caption
            ]);

            return response()->json([
                'success' => true,
                'message' => 'อัปเดตคำบรรยายสำเร็จ'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating image caption: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการอัปเดตคำบรรยาย'
            ], 500);
        }
    }

    /**
     * Update images sort order
     */
    public function updateImageOrder(Request $request, Activity $activity)
    {
        $request->validate([
            'image_ids' => 'required|array',
            'image_ids.*' => 'exists:activity_images,id'
        ]);

        foreach ($request->image_ids as $index => $imageId) {
            ActivityImage::where('id', $imageId)
                ->where('activity_id', $activity->id)
                ->update(['sort_order' => $index]);
        }

        return response()->json([
            'success' => true,
            'message' => 'จัดเรียงรูปภาพสำเร็จ'
        ]);
    }

    /**
     * Replace an existing image
     */
    public function replaceImage(Request $request, ActivityImage $image)
    {
        try {
            $request->validate([
                'new_image' => 'required|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
                'caption' => 'nullable|string|max:255',
            ]);

            $imageErrors = ImageHelper::validateImage($request->file('new_image'));
            if (!empty($imageErrors)) {
                return response()->json([
                    'success' => false,
                    'message' => implode(', ', $imageErrors)
                ], 422);
            }

            // Delete old image
            ImageHelper::deleteImage($image->image_path);

            // Upload new image
            $newImagePath = ImageHelper::uploadAndResize($request->file('new_image'), 'activities/gallery');

            // Update image record
            $image->update([
                'image_path' => $newImagePath,
                'caption' => $request->caption ?? $image->caption
            ]);

            return response()->json([
                'success' => true,
                'message' => 'เปลี่ยนรูปภาพสำเร็จ',
                'new_image_url' => ImageHelper::getImageUrl($newImagePath)
            ]);
        } catch (\Exception $e) {
            \Log::error('Error replacing image: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการเปลี่ยนรูปภาพ'
            ], 500);
        }
    }
}
