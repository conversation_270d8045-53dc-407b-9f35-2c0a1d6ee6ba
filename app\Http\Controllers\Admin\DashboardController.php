<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use App\Models\Package;
use App\Models\Activity;
use App\Models\Contact;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Get statistics for dashboard
        $servicesCount = Service::count();
        $packagesCount = Package::count();
        $activitiesCount = Activity::count();
        $unreadContactsCount = Contact::where('is_read', false)->count();
        
        // Get recent contacts
        $recentContacts = Contact::latest()->take(5)->get();
        
        return view('admin.dashboard', compact(
            'servicesCount',
            'packagesCount', 
            'activitiesCount',
            'unreadContactsCount',
            'recentContacts'
        ));
    }
}
