@extends('layouts.admin')

@section('title', 'จัดการแพ็กเกจ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-box me-2"></i>จัดการแพ็กเกจ
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item active">จัดการแพ็กเกจ</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">รายการแพ็กเกจทั้งหมด</h3>
                            <div class="card-tools">
                                <div class="input-group input-group-sm" style="width: 250px;">
                                    <input type="text" name="search" class="form-control float-right" placeholder="ค้นหาแพ็กเกจ..." id="searchInput">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-default" id="searchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="clearSearchBtn" style="display: none;">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="btn-group ml-2">
                                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                                        <i class="fas fa-filter me-1"></i>ตัวกรอง
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="#" onclick="filterByPrice('all')">ทั้งหมด</a>
                                        <a class="dropdown-item" href="#" onclick="filterByPrice('low')">ราคาต่ำ (< 5,000)</a>
                                        <a class="dropdown-item" href="#" onclick="filterByPrice('medium')">ราคาปานกลาง (5,000-20,000)</a>
                                        <a class="dropdown-item" href="#" onclick="filterByPrice('high')">ราคาสูง (> 20,000)</a>
                                    </div>
                                </div>
                                <a href="{{ route('admin.packages.create') }}" class="btn btn-primary ml-2">
                                    <i class="fas fa-plus me-1"></i>เพิ่มแพ็กเกจใหม่
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible">
                                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                                    {{ session('success') }}
                                </div>
                            @endif

                            @if($packages->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th style="width: 120px;">รูปภาพ</th>
                                                <th>ชื่อแพ็กเกจ</th>
                                                <th>รายละเอียด</th>
                                                <th style="width: 120px;">ราคา</th>
                                                <th style="width: 150px;">จัดการ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($packages as $package)
                                                <tr>
                                                    <td class="text-center">
                                                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($package->image) }}"
                                                             class="img-thumbnail"
                                                             style="width: 100px; height: 70px; object-fit: cover;"
                                                             alt="รูปภาพแพ็กเกจ">
                                                    </td>
                                                    <td>
                                                        <strong>{{ $package->name }}</strong>
                                                    </td>
                                                    <td>
                                                        {{ Str::limit($package->description, 80) }}
                                                    </td>
                                                    <td class="text-right">
                                                        <span class="badge badge-success">
                                                            ฿{{ number_format($package->price, 0) }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('admin.packages.edit', $package) }}"
                                                               class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <form action="{{ route('admin.packages.destroy', $package) }}"
                                                                  method="POST"
                                                                  style="display:inline;"
                                                                  onsubmit="return confirm('ยืนยันการลบแพ็กเกจ {{ $package->name }}?')">
                                                                @csrf @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-danger">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">ยังไม่มีแพ็กเกจ</h5>
                                    <p class="text-muted">เริ่มต้นโดยการเพิ่มแพ็กเกจแรกของคุณ</p>
                                    <a href="{{ route('admin.packages.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>เพิ่มแพ็กเกจใหม่
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Search functionality
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');

    tableRows.forEach(row => {
        const name = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const description = row.querySelector('td:nth-child(3)').textContent.toLowerCase();

        if (name.includes(searchTerm) || description.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});
</script>
@endsection