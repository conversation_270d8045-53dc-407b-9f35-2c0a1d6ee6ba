<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;

class ServiceController extends BaseResourceController
{
    protected function getModelClass()
    {
        return Service::class;
    }

    protected function getViewPrefix()
    {
        return 'services';
    }

    protected function getRoutePrefix()
    {
        return 'services';
    }

    protected function getImageDirectory()
    {
        return 'services';
    }

    protected function getValidationRules(Request $request, $model = null)
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ];
    }

    // หน้าบ้าน: แสดงรายละเอียดบริการ
    public function show(Service $service)
    {
        // Handle AJAX request for admin
        $ajaxResponse = $this->handleAjaxShow($service, [
            'title', 'description', 'price', 'image_url'
        ]);

        if ($ajaxResponse) {
            return $ajaxResponse;
        }

        return view('services.show', compact('service'));
    }

    // Override update method to handle AJAX requests
    public function update(Request $request, $model)
    {
        // ตรวจสอบว่าเป็น AJAX request สำหรับ quick edit หรือไม่
        if ($request->ajax()) {
            $data = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'price' => 'required|numeric|min:0',
            ]);

            $model->update($data);

            return response()->json([
                'success' => true,
                'message' => 'อัปเดตบริการสำเร็จ',
                'service' => $model
            ]);
        }

        // Use parent method for normal updates
        return parent::update($request, $model);
    }

    // หลังบ้าน: ลบบริการหลายรายการ
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:services,id'
        ]);

        try {
            $services = Service::whereIn('id', $request->ids)->get();

            // Delete images first
            foreach ($services as $service) {
                if ($service->image) {
                    ImageHelper::deleteImage($service->image);
                }
            }

            // Delete services
            Service::whereIn('id', $request->ids)->delete();

            return response()->json([
                'success' => true,
                'message' => 'ลบบริการ ' . count($request->ids) . ' รายการสำเร็จ'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบข้อมูล'
            ], 500);
        }
    }
}