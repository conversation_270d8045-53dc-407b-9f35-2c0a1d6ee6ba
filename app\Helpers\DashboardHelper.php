<?php

namespace App\Helpers;

use App\Models\Service;
use App\Models\Package;
use App\Models\Activity;
use App\Models\Contact;
use Carbon\Carbon;

class DashboardHelper
{
    /**
     * Get dashboard statistics
     *
     * @return array
     */
    public static function getStatistics()
    {
        try {
            return [
                'services' => [
                    'total' => Service::count(),
                    'recent' => Service::latest()->take(5)->get(),
                ],
                'packages' => [
                    'total' => Package::count(),
                    'recent' => Package::latest()->take(5)->get(),
                ],
                'activities' => [
                    'total' => Activity::count(),
                    'published' => Activity::where('is_published', true)->count(),
                    'recent' => Activity::latest()->take(5)->get(),
                ],
                'contacts' => [
                    'total' => Contact::count(),
                    'unread' => Contact::where('is_read', false)->count(),
                    'today' => Contact::whereDate('created_at', today())->count(),
                    'week' => Contact::whereBetween('created_at', [
                        Carbon::now()->startOfWeek(),
                        Carbon::now()->endOfWeek()
                    ])->count(),
                    'month' => Contact::whereMonth('created_at', Carbon::now()->month)
                                    ->whereYear('created_at', Carbon::now()->year)
                                    ->count(),
                    'recent' => Contact::latest()->take(5)->get(),
                ],
                'revenue' => [
                    'services_total' => Service::sum('price'),
                    'packages_total' => Package::sum('price'),
                    'average_service' => Service::avg('price'),
                    'average_package' => Package::avg('price'),
                ]
            ];
        } catch (\Exception $e) {
            \Log::error('Dashboard statistics error: ' . $e->getMessage());
            return self::getDefaultStatistics();
        }
    }

    /**
     * Get default statistics when database is not available
     *
     * @return array
     */
    private static function getDefaultStatistics()
    {
        return [
            'services' => ['total' => 0, 'recent' => collect()],
            'packages' => ['total' => 0, 'recent' => collect()],
            'activities' => ['total' => 0, 'published' => 0, 'recent' => collect()],
            'contacts' => [
                'total' => 0, 'unread' => 0, 'today' => 0, 
                'week' => 0, 'month' => 0, 'recent' => collect()
            ],
            'revenue' => [
                'services_total' => 0, 'packages_total' => 0,
                'average_service' => 0, 'average_package' => 0
            ]
        ];
    }

    /**
     * Get contact statistics for specific period
     *
     * @param string $period
     * @return int
     */
    public static function getContactsForPeriod($period = 'today')
    {
        try {
            switch ($period) {
                case 'today':
                    return Contact::whereDate('created_at', today())->count();
                case 'week':
                    return Contact::whereBetween('created_at', [
                        Carbon::now()->startOfWeek(),
                        Carbon::now()->endOfWeek()
                    ])->count();
                case 'month':
                    return Contact::whereMonth('created_at', Carbon::now()->month)
                                  ->whereYear('created_at', Carbon::now()->year)
                                  ->count();
                case 'year':
                    return Contact::whereYear('created_at', Carbon::now()->year)->count();
                default:
                    return Contact::count();
            }
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get growth percentage
     *
     * @param string $model
     * @param string $period
     * @return float
     */
    public static function getGrowthPercentage($model, $period = 'month')
    {
        try {
            $modelClass = "App\\Models\\{$model}";
            
            if (!class_exists($modelClass)) {
                return 0;
            }

            $current = $modelClass::whereMonth('created_at', Carbon::now()->month)
                                 ->whereYear('created_at', Carbon::now()->year)
                                 ->count();

            $previous = $modelClass::whereMonth('created_at', Carbon::now()->subMonth()->month)
                                  ->whereYear('created_at', Carbon::now()->subMonth()->year)
                                  ->count();

            if ($previous == 0) {
                return $current > 0 ? 100 : 0;
            }

            return round((($current - $previous) / $previous) * 100, 1);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Format number for display
     *
     * @param mixed $number
     * @return string
     */
    public static function formatNumber($number)
    {
        if ($number >= 1000000) {
            return round($number / 1000000, 1) . 'M';
        } elseif ($number >= 1000) {
            return round($number / 1000, 1) . 'K';
        }
        return number_format($number);
    }

    /**
     * Format currency for display
     *
     * @param mixed $amount
     * @return string
     */
    public static function formatCurrency($amount)
    {
        return '฿' . number_format($amount, 0);
    }

    /**
     * Get system health status
     *
     * @return array
     */
    public static function getSystemHealth()
    {
        $health = [
            'database' => false,
            'storage' => false,
            'cache' => false,
            'overall' => 'poor'
        ];

        try {
            // Test database connection
            \DB::connection()->getPdo();
            $health['database'] = true;
        } catch (\Exception $e) {
            \Log::error('Database health check failed: ' . $e->getMessage());
        }

        try {
            // Test storage
            $testFile = 'health_check_' . time() . '.txt';
            \Storage::disk('public')->put($testFile, 'test');
            \Storage::disk('public')->delete($testFile);
            $health['storage'] = true;
        } catch (\Exception $e) {
            \Log::error('Storage health check failed: ' . $e->getMessage());
        }

        try {
            // Test cache
            \Cache::put('health_check', 'test', 60);
            $health['cache'] = \Cache::get('health_check') === 'test';
            \Cache::forget('health_check');
        } catch (\Exception $e) {
            \Log::error('Cache health check failed: ' . $e->getMessage());
        }

        // Calculate overall health
        $healthyComponents = array_filter($health, function($status) {
            return $status === true;
        });

        $healthPercentage = (count($healthyComponents) / 3) * 100;

        if ($healthPercentage >= 80) {
            $health['overall'] = 'excellent';
        } elseif ($healthPercentage >= 60) {
            $health['overall'] = 'good';
        } elseif ($healthPercentage >= 40) {
            $health['overall'] = 'fair';
        } else {
            $health['overall'] = 'poor';
        }

        return $health;
    }
}
